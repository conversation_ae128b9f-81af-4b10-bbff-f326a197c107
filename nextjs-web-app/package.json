{"name": "web-app-generator", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "deploy": "ts-node deploy.ts"}, "dependencies": {"@aws-sdk/client-bedrock-runtime": "^3.758.0", "@e2b/code-interpreter": "^1.0.4", "@e2b/sdk": "^0.12.5", "@heroicons/react": "^2.2.0", "@monaco-editor/react": "^4.6.0", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-slot": "^1.1.2", "@vercel/client": "^15.1.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^11.18.2", "jotai": "^2.12.1", "lucide-react": "^0.477.0", "nanoid": "^5.1.2", "next": "15.2.0", "openai": "^4.86.1", "portkey-ai": "^1.7.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "replicate": "^1.0.1", "styled-components": "^6.1.15", "tailwind-merge": "^3.0.2", "vercel": "^41.3.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20.17.22", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.2.0", "tailwindcss": "^4", "ts-node": "^10.9.2", "typescript": "^5"}}